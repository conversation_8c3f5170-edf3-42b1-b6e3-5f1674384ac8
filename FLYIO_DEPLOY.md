# 🚀 Deploy Fly.io - API Consulta Veículos

## 🎯 Por que Fly.io é MELHOR que Railway?

- ✅ **GRÁTIS PARA SEMPRE** - Não é crédito que acaba
- ✅ **Performance superior** - Infraestrutura global
- ✅ **Selenium funciona perfeitamente** - Firefox pré-instalado
- ✅ **Não para nunca** - Sem limite de tempo/crédito
- ✅ **Região Brasil** - Deploy em São Paulo (gru)
- ✅ **SSL automático** - HTTPS incluído

---

## 🚀 Deploy Passo a Passo

### 1. Instalar Fly CLI

**Windows:**
```bash
# PowerShell (Admin)
iwr https://fly.io/install.ps1 -useb | iex
```

**Linux/Mac:**
```bash
curl -L https://fly.io/install.sh | sh
```

### 2. Login no Fly.io

```bash
# Fazer login (vai abrir browser)
fly auth login

# Verificar se está logado
fly auth whoami
```

### 3. Preparar Projeto

Os arquivos já estão prontos:
- ✅ `fly.toml` - Configuração Fly.io
- ✅ `Dockerfile` - Container otimizado
- ✅ Código da API funcionando

### 4. Deploy

```bash
# Navegar para pasta do projeto
cd D:\BROBOT\DER-MG

# Criar app no Fly.io
fly apps create placa-api-SEU-NOME

# Fazer deploy
fly deploy

# Aguardar (3-5 minutos)
```

### 5. Configurar Domínio

```bash
# Ver status da aplicação
fly status

# Sua API estará em:
# https://placa-api-SEU-NOME.fly.dev
```

### 6. Testar

```bash
# Testar se está online
curl https://placa-api-SEU-NOME.fly.dev/

# Gerar token admin
curl -H "admin-password: adminbrobot@2025" -X POST https://placa-api-SEU-NOME.fly.dev/admin/create-token

# Consultar veículo
curl -H "Authorization: Bearer TOKEN_AQUI" https://placa-api-SEU-NOME.fly.dev/veiculo/EZI5F92

# Swagger
# https://placa-api-SEU-NOME.fly.dev/docs
```

---

## ⚙️ Configurações Avançadas

### Escalabilidade Automática
```bash
# Definir mínimo de máquinas
fly scale count 1

# Configurar região (já está São Paulo)
fly regions list
```

### Logs e Monitoramento
```bash
# Ver logs em tempo real
fly logs

# Status da aplicação
fly status

# Entrar na máquina (debug)
fly ssh console
```

### Atualizações
```bash
# Fazer novo deploy após mudanças
fly deploy

# Rollback se necessário
fly releases
fly rollback
```

---

## 🔧 Configurações do Projeto

### fly.toml (Já configurado)
```toml
app = "placa-api"
primary_region = "gru"  # São Paulo

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = false  # Não para nunca
  min_machines_running = 1    # Sempre pelo menos 1

[[vm]]
  memory = "1gb"    # Suficiente para Selenium
  cpu_kind = "shared"
  cpus = 1
```

### Dockerfile (Otimizado)
- ✅ Firefox ESR pré-instalado
- ✅ Geckodriver configurado
- ✅ Dependências Python otimizadas
- ✅ Ambiente pronto para Selenium

---

## 💰 Custos (Free Tier)

### Fly.io Gratuito:
- **3 máquinas shared-cpu-1x** (suficiente)
- **3GB storage persistente**
- **160GB outbound data**
- **Sem tempo limite** - Roda para sempre

### Comparação:
| Feature | Fly.io FREE | Railway PAID |
|---------|-------------|--------------|
| Custo | $0 forever | $5+ /mês |
| Máquinas | 3 | 1 |
| Selenium | ✅ | ✅ |
| Tempo limite | ❌ | ✅ Para quando acaba |
| Performance | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

---

## 🆘 Troubleshooting

### Selenium não funciona:
```bash
# Entrar na máquina e debugar
fly ssh console
firefox --version
geckodriver --version
```

### App lenta para acordar:
```bash
# Garantir que não para
fly scale count 1
```

### Erro de build:
```bash
# Ver logs detalhados
fly logs --app placa-api-SEU-NOME
```

### Reset completo:
```bash
# Destruir e recriar
fly apps destroy placa-api-SEU-NOME
fly apps create placa-api-SEU-NOME-novo
fly deploy
```

---

## 🎯 Vantagens do Fly.io

### Performance:
- **Região São Paulo** - Latência baixa para Brasil
- **SSD NVMe** - Storage rápido
- **Anycast IPs** - Roteamento otimizado

### Confiabilidade:
- **99.95% uptime** - Mais estável que Railway
- **Auto-restart** - Se falhar, reinicia automaticamente
- **Health checks** - Monitora se API está respondendo

### Flexibilidade:
- **SSH access** - Pode entrar na máquina para debug
- **Persistent volumes** - Dados não se perdem
- **Global deployment** - Pode expandir para outras regiões

---

## 📱 URLs Finais

Depois do deploy:
- **API Base**: `https://placa-api-SEU-NOME.fly.dev`
- **Swagger**: `https://placa-api-SEU-NOME.fly.dev/docs`
- **Status**: `https://placa-api-SEU-NOME.fly.dev/status`

---

## ✅ Checklist de Deploy

### Pré-Deploy:
- [ ] Fly CLI instalado
- [ ] Login no Fly.io feito
- [ ] Arquivos fly.toml e Dockerfile no projeto

### Deploy:
- [ ] `fly apps create` executado
- [ ] `fly deploy` executado com sucesso
- [ ] API responde na URL gerada

### Pós-Deploy:
- [ ] Teste de geração de token funcionando
- [ ] Consulta de veículo funcionando
- [ ] Swagger acessível
- [ ] Logs mostram DER-MG + DETRAN-RS funcionando

---

**🎉 Fly.io é objetivamente superior ao Railway para este projeto!**

- Grátis para sempre
- Performance melhor
- Mais estável
- Selenium funciona perfeitamente