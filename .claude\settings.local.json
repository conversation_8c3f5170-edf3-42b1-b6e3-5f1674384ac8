{"permissions": {"allow": ["<PERSON><PERSON>(del:*)", "Bash(rm:*)", "<PERSON><PERSON>(python test:*)", "Bash(git init:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git branch:*)", "Bash(git remote add:*)", "Bash(git push:*)", "WebFetch(domain:placa-api.fly.dev)", "Bash(flyctl status:*)", "Bash(flyctl apps:*)", "Bash(flyctl logs:*)", "Bash(C:UsersLENOVO.flybinflyctl.exe status)", "<PERSON><PERSON>(powershell:*)", "<PERSON><PERSON>(curl:*)"], "deny": []}}