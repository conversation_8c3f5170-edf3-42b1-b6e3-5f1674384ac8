from fastapi import FastAP<PERSON>, HTTPException, Path, Depends, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
import time
import sqlite3
import secrets
import hashlib
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from urllib.parse import urljoin
import uvicorn
from typing import Optional, Dict, Any, List
import threading
import atexit
import json
import os
import requests

# Modelos Pydantic para Swagger
class VehicleData(BaseModel):
    """Dados completos do veículo - Sistema Híbrido DER-MG + DETRAN-RS"""
    placa: str = Field(..., description="Placa do veículo", example="EZI5F92")
    
    # Dados do DER-MG (proprietário)
    nome_proprietario: Optional[str] = Field(None, description="Nome do proprietário", example="JOÃO DA SILVA")
    cpf_cnpj: Optional[str] = Field(None, description="CPF/CNPJ do proprietário", example="12345678901")
    renavam: Optional[str] = Field(None, description="Número do RENAVAM", example="341684325")
    
    # Dados do DETRAN-RS (técnicos)
    marca: Optional[str] = Field(None, description="Marca do veículo", example="CITROEN")
    modelo: Optional[str] = Field(None, description="Modelo do veículo", example="C4 20EXCA5P F")
    cor: Optional[str] = Field(None, description="Cor do veículo", example="Prata")
    situacao: Optional[str] = Field(None, description="Situação do veículo", example="Veículo em circulação")
    chassi: Optional[str] = Field(None, description="Número do chassi", example="8BCLCRFJVBG545031")
    ano_fabricacao: Optional[int] = Field(None, description="Ano de fabricação", example=2011)
    ano_modelo: Optional[int] = Field(None, description="Ano do modelo", example=2011)
    tipo: Optional[str] = Field(None, description="Tipo do veículo", example="Automóvel")
    especie: Optional[str] = Field(None, description="Espécie do veículo", example="Passageiro")
    municipio_registro: Optional[str] = Field(None, description="Município de registro", example="ITAPETININGA")
    uf: Optional[str] = Field(None, description="Estado de registro", example="SP")
    furtado: Optional[bool] = Field(None, description="Se o veículo foi furtado", example=False)
    em_deposito: Optional[bool] = Field(None, description="Se está em depósito", example=False)

class VehicleResponse(BaseModel):
    """Resposta da consulta do veículo"""
    success: bool = Field(..., description="Status da operação")
    data: VehicleData = Field(..., description="Dados do veículo")
    timestamp: str = Field(..., description="Timestamp da consulta", example="2024-01-15T10:30:00")

class StatusResponse(BaseModel):
    """Status da sessão Selenium"""
    driver_active: bool = Field(..., description="Se o driver está ativo")
    logged_in: bool = Field(..., description="Se está logado no sistema")
    last_activity: Optional[str] = Field(None, description="Última atividade da sessão")
    session_age_seconds: Optional[float] = Field(None, description="Idade da sessão em segundos")

class ErrorResponse(BaseModel):
    """Resposta de erro"""
    detail: str = Field(..., description="Descrição do erro")

class MessageResponse(BaseModel):
    """Resposta de mensagem simples"""
    message: str = Field(..., description="Mensagem de resposta")

class TokenResponse(BaseModel):
    """Resposta de criação de token"""
    token: str = Field(..., description="Token de acesso gerado")
    created_at: str = Field(..., description="Data de criação")
    usage_count: int = Field(0, description="Número de usos")

class TokenUsage(BaseModel):
    """Uso do token"""
    token: str = Field(..., description="Token")
    usage_count: int = Field(..., description="Número de usos")
    created_at: str = Field(..., description="Data de criação")
    last_used: Optional[str] = Field(None, description="Último uso")

app = FastAPI(
    title="API Consulta Veículos",
    description="""
    ## 🚗 API para Consulta de Dados Veiculares
    
    ### Autenticação
    Todas as consultas requerem um token de acesso válido no header `Authorization: Bearer {token}`
    
    ### Como usar
    1. Obtenha um token de acesso válido
    2. Use o token para consultar dados de veículos via `/veiculo/{placa}`
    3. Receba informações completas em formato JSON
    
    ### Exemplo de uso
    ```bash
    curl -H "Authorization: Bearer seu_token_aqui" http://localhost:8000/veiculo/ABC1234
    ```
    
    ### Formatos de placa suportados
    - Formato antigo: ABC1234
    - Formato Mercosul: ABC1D23
    """,
    version="1.0.0"
)

# Configurações
ADMIN_PASSWORD = "adminbrobot@2025"
security = HTTPBearer()

# Database setup com persistência
import os
DB_PATH = '/app/data/tokens.db' if os.path.exists('/app/data') else 'tokens.db'

def init_db():
    # Criar diretório se não existir
    os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
    
    conn = sqlite3.connect(DB_PATH)
    conn.execute('''
        CREATE TABLE IF NOT EXISTS tokens (
            token TEXT PRIMARY KEY,
            created_at TEXT,
            usage_count INTEGER DEFAULT 0,
            last_used TEXT
        )
    ''')
    conn.commit()
    conn.close()

init_db()

class SeleniumService:
    def __init__(self):
        self.base_url = "https://portal.der.mg.gov.br/saeptl/"
        self.username = "28270178810"
        self.password = "fvq7f6u>xm"
        self.driver = None
        self.logged_in = False
        self.last_activity = None
        self.lock = threading.Lock()
        self.session_timeout = 14400  # 4 horas - sessão mais longa
        
        # Inicializar driver
        self._initialize_driver()
        
        # Registrar cleanup
        atexit.register(self.cleanup)
        
        # URLs para DETRAN-RS
        self.detran_rs_url = "https://secweb.procergs.com.br/pcsdetran/rest/veiculos/simplificada"
    
    def _initialize_driver(self):
        """Inicializa o driver Firefox"""
        try:
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
            
            firefox_options = FirefoxOptions()
            firefox_options.add_argument("--headless")
            firefox_options.set_preference("browser.download.folderList", 2)
            firefox_options.set_preference("browser.download.manager.showWhenStarting", False)
            firefox_options.set_preference("browser.helperApps.neverAsk.saveToDisk", "application/x-gzip")
            
            self.driver = webdriver.Firefox(options=firefox_options)
            self.driver.set_page_load_timeout(30)
            self.driver.set_script_timeout(30)
            
            print("Driver Firefox inicializado")
            return True
            
        except Exception as e:
            print(f"Erro ao inicializar driver: {e}")
            self.driver = None
            return False
    
    def _is_logged_in(self) -> bool:
        """Verifica se ainda está logado"""
        if not self.driver:
            return False
            
        try:
            # Verificar se está na página correta e logado
            current_url = self.driver.current_url
            if "inicial.do" in current_url or "veiculosrequerenteptlman.do" in current_url:
                return True
            
            # Tentar acessar uma página que requer login
            self.driver.get(urljoin(self.base_url, "veiculosrequerenteptlman.do"))
            time.sleep(2)
            
            # Se não foi redirecionado para login, ainda está logado
            if "login" not in self.driver.current_url.lower():
                return True
                
        except Exception as e:
            print(f"Erro ao verificar login: {e}")
        
        return False
    
    def _login(self) -> bool:
        """Faz login no sistema - VERSÃO ROBUSTA"""
        try:
            print("Fazendo login no Firefox...")
            self.driver.get(self.base_url)

            # Aguardar a página carregar completamente
            time.sleep(3)
            
            wait = WebDriverWait(self.driver, 15)

            # Tentar diferentes métodos para encontrar os campos
            username_field = None
            password_field = None
            
            try:
                # Método 1: IDs originais
                username_field = wait.until(EC.presence_of_element_located((By.ID, "j_username")))
                password_field = self.driver.find_element(By.ID, "j_password")
            except:
                try:
                    # Método 2: Por nome
                    username_field = self.driver.find_element(By.NAME, "j_username")
                    password_field = self.driver.find_element(By.NAME, "j_password")
                except:
                    try:
                        # Método 3: Por tipo de input
                        inputs = self.driver.find_elements(By.TAG_NAME, "input")
                        for inp in inputs:
                            if inp.get_attribute("type") == "text":
                                username_field = inp
                            elif inp.get_attribute("type") == "password":
                                password_field = inp
                    except:
                        pass

            if not username_field or not password_field:
                raise Exception("Campos de login não encontrados")

            # Limpar e preencher campos
            username_field.clear()
            password_field.clear()
            username_field.send_keys(self.username)
            password_field.send_keys(self.password)

            # Tentar encontrar botão de submit
            try:
                submit_btn = self.driver.find_element(By.XPATH, "//input[@value='Entrar']")
                submit_btn.click()
            except:
                try:
                    submit_btn = self.driver.find_element(By.XPATH, "//input[@type='submit']")
                    submit_btn.click()
                except:
                    try:
                        submit_btn = self.driver.find_element(By.XPATH, "//button[@type='submit']")
                        submit_btn.click()
                    except:
                        # Se não encontrar botão, pressionar Enter no campo de senha
                        from selenium.webdriver.common.keys import Keys
                        password_field.send_keys(Keys.RETURN)

            # Aguardar redirecionamento com timeout maior
            wait = WebDriverWait(self.driver, 20)
            wait.until(lambda driver: 
                "inicial.do" in driver.current_url or 
                "veiculosrequerenteptlman.do" in driver.current_url or
                driver.current_url != self.base_url
            )
            
            print("Login no Firefox realizado com sucesso")
            self.logged_in = True
            self.last_activity = datetime.now()
            return True

        except Exception as e:
            print(f"Erro ao fazer login: {str(e)}")
            print(f"URL atual: {self.driver.current_url if self.driver else 'N/A'}")
            self.logged_in = False
            return False
    
    def _ensure_session(self) -> bool:
        """Garante que a sessão está ativa"""
        with self.lock:
            # Verificar se driver existe
            if not self.driver:
                if not self._initialize_driver():
                    return False
            
            # Verificar timeout da sessão
            if (self.last_activity and 
                datetime.now() - self.last_activity > timedelta(seconds=self.session_timeout)):
                print("Sessao expirou por timeout")
                self.logged_in = False
            
            # Verificar se ainda está logado
            if not self.logged_in or not self._is_logged_in():
                print("Sessao perdida, fazendo novo login...")
                return self._login()
            
            # Atualizar última atividade
            self.last_activity = datetime.now()
            return True
    
    def get_vehicle_data(self, plate: str) -> Optional[VehicleData]:
        """Obtém dados híbridos do veículo - DER-MG + DETRAN-RS"""
        print(f"=== SISTEMA HÍBRIDO INICIADO PARA {plate} ===")
        
        # ETAPA 1: DER-MG (Dados do proprietário + RENAVAM)
        der_mg_data = self._get_der_mg_data(plate)
        if not der_mg_data:
            print("Falha ao obter dados do DER-MG")
            return None
        
        # ETAPA 2: DETRAN-RS (Dados técnicos)
        detran_rs_data = None
        if der_mg_data.get('renavam'):
            detran_rs_data = self.get_detran_rs_data(plate, der_mg_data['renavam'])
        
        # ETAPA 3: MERGE dos dados
        return self._merge_vehicle_data(plate, der_mg_data, detran_rs_data)
    
    def _get_der_mg_data(self, plate: str) -> Optional[Dict[str, Any]]:
        """Obtém dados essenciais do DER-MG: proprietário, CPF, RENAVAM"""
        if not self._ensure_session():
            raise Exception("Não foi possível estabelecer sessão")

        try:
            print(f"Consultando DER-MG para placa {plate}...")
            wait = WebDriverWait(self.driver, 10)

            # Ir para a página de veículos
            vehicles_url = urljoin(self.base_url, "veiculosrequerenteptlman.do?evento=F7-Novo")
            self.driver.get(vehicles_url)

            # Inserir placa
            plate_field = wait.until(EC.presence_of_element_located((By.ID, "dePlaca")))
            plate_field.clear()
            plate_field.send_keys(plate)

            # Disparar carregamento
            self.driver.find_element(By.TAG_NAME, "body").click()
            time.sleep(2)

            # Tratar alertas
            try:
                alert = self.driver.switch_to.alert
                print(f"Alerta DER-MG: {alert.text}")
                alert.accept()
                time.sleep(1)
            except:
                pass

            # Aguardar carregamento
            try:
                wait.until(lambda driver:
                    driver.find_element(By.ID, "nmProprietario").get_attribute("value") != "" or
                    driver.find_element(By.ID, "nuCpfCnpjStr").get_attribute("value") != "" or
                    driver.find_element(By.ID, "nuRenavam").get_attribute("value") != ""
                )
            except:
                pass

            # Extrair APENAS os dados essenciais
            vehicle_data = {'placa': plate}
            
            essential_fields = {
                'nmProprietario': 'nome_proprietario',
                'nuCpfCnpjStr': 'cpf_cnpj', 
                'nuRenavam': 'renavam'
            }

            for field_id, field_name in essential_fields.items():
                try:
                    field_value = self.driver.find_element(By.ID, field_id).get_attribute("value")
                    if field_value:
                        vehicle_data[field_name] = field_value.strip()
                        print(f"DER-MG {field_name}: {field_value}")
                except:
                    pass

            self.last_activity = datetime.now()
            print(f"DER-MG concluído para {plate}")
            
            return vehicle_data if len(vehicle_data) > 1 else None

        except Exception as e:
            print(f"Erro no DER-MG: {str(e)}")
            self.logged_in = False
            raise
    
    def _merge_vehicle_data(self, plate: str, der_mg_data: Dict[str, Any], detran_rs_data: Optional[Dict[str, Any]]) -> Optional[VehicleData]:
        """Combina dados do DER-MG e DETRAN-RS"""
        try:
            print(f"Fazendo merge dos dados para {plate}...")
            
            # Base com dados do DER-MG
            merged_data = {
                'placa': plate,
                'nome_proprietario': der_mg_data.get('nome_proprietario'),
                'cpf_cnpj': der_mg_data.get('cpf_cnpj'),
                'renavam': der_mg_data.get('renavam')
            }
            
            # Adicionar dados do DETRAN-RS se disponíveis
            if detran_rs_data:
                # Separar marca e modelo se vierem juntos
                marca_modelo = detran_rs_data.get('marcaModelo', '')
                if marca_modelo:
                    parts = marca_modelo.split(' ', 1)
                    if len(parts) >= 2:
                        # Remove prefixos como "I/" se existirem
                        marca = parts[0].replace('I/', '').strip()
                        modelo = parts[1].strip()
                    else:
                        marca = marca_modelo
                        modelo = None
                else:
                    marca = None
                    modelo = None
                
                merged_data.update({
                    'marca': marca,
                    'modelo': modelo,
                    'cor': detran_rs_data.get('cor'),
                    'situacao': detran_rs_data.get('situacao'),
                    'chassi': detran_rs_data.get('chassi'),
                    'ano_fabricacao': detran_rs_data.get('anoFabricacao'),
                    'ano_modelo': detran_rs_data.get('anoModelo'),
                    'tipo': detran_rs_data.get('tipo'),
                    'especie': detran_rs_data.get('especie'),
                    'municipio_registro': detran_rs_data.get('municipioRegistro'),
                    'uf': detran_rs_data.get('ufPlaca'),
                    'furtado': detran_rs_data.get('furtado', False),
                    'em_deposito': detran_rs_data.get('emDeposito', False)
                })
                
                print(f"Merge concluído com dados do DETRAN-RS")
            else:
                print(f"Merge usando apenas dados do DER-MG (DETRAN-RS indisponível)")
            
            # Criar objeto VehicleData
            return VehicleData(**merged_data)
            
        except Exception as e:
            print(f"Erro no merge dos dados: {str(e)}")
            return None
    
    def get_detran_rs_data(self, placa: str, renavam: str) -> Optional[Dict[str, Any]]:
        """Obtém dados técnicos do DETRAN-RS"""
        try:
            print(f"Consultando DETRAN-RS para placa {placa} com RENAVAM {renavam}")
            
            # Headers para simular browser
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Accept-Language': 'en-US,en;q=0.9',
                'Connection': 'keep-alive',
                'Host': 'secweb.procergs.com.br',
                'Origin': 'https://pcsdetran.rs.gov.br',
                'Referer': 'https://pcsdetran.rs.gov.br/',
                'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'cross-site',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
            
            # Parâmetros da requisição
            params = {
                'placa': placa.upper(),
                'renavam': renavam,
                'somenteRS': 'false'
            }
            
            # Fazer requisição
            response = requests.get(
                self.detran_rs_url, 
                params=params, 
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Verificar se houve erro
                if data.get('temErro', True) or data.get('erro'):
                    print(f"DETRAN-RS retornou erro: {data.get('erro', 'Erro desconhecido')}")
                    return None
                
                print(f"DETRAN-RS obteve dados com sucesso para {placa}")
                return data
            else:
                print(f"DETRAN-RS retornou status {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Erro ao consultar DETRAN-RS: {str(e)}")
            return None
    
    def _extract_uf(self, vehicle_data: Dict[str, Any]):
        """Extrai UF usando os métodos da spider original"""
        try:
            # Método 1: XPath direto
            try:
                uf_label = self.driver.find_element(By.XPATH, "//td[text()='UF']/following-sibling::td/select")
                selected_value = self.driver.execute_script("return arguments[0].options[arguments[0].selectedIndex].text;", uf_label)
                if selected_value:
                    vehicle_data['uf'] = selected_value.strip()
                    return
            except:
                pass
            
            # Método 2: Por ID/nome
            for selector in ["cdUf", "uf", "unidadeFederal", "sgUf"]:
                try:
                    uf_select = self.driver.find_element(By.ID, selector)
                    selected_value = self.driver.execute_script("return arguments[0].options[arguments[0].selectedIndex].text;", uf_select)
                    if selected_value:
                        vehicle_data['uf'] = selected_value.strip()
                        return
                except:
                    try:
                        uf_select = self.driver.find_element(By.NAME, selector)
                        selected_value = self.driver.execute_script("return arguments[0].options[arguments[0].selectedIndex].text;", uf_select)
                        if selected_value:
                            vehicle_data['uf'] = selected_value.strip()
                            return
                    except:
                        pass
            
            # Método 3: Análise de todos os selects
            ufs = ["AC", "AL", "AP", "AM", "BA", "CE", "DF", "ES", "GO", "MA", "MT", "MS", "MG", "PA", "PB", "PR", "PE", "PI", "RJ", "RN", "RS", "RO", "RR", "SC", "SP", "SE", "TO"]
            all_selects = self.driver.find_elements(By.TAG_NAME, "select")
            
            for select in all_selects:
                try:
                    options = select.find_elements(By.TAG_NAME, "option")
                    option_texts = [option.text.strip() for option in options]
                    uf_count = sum(1 for text in option_texts if text in ufs)
                    
                    if uf_count >= 5:
                        selected_value = self.driver.execute_script("return arguments[0].options[arguments[0].selectedIndex].text;", select)
                        if selected_value and selected_value in ufs:
                            vehicle_data['uf'] = selected_value.strip()
                            return
                except:
                    continue
        except:
            pass
    
    def get_status(self) -> StatusResponse:
        """Retorna status da sessão"""
        return StatusResponse(
            driver_active=self.driver is not None,
            logged_in=self.logged_in,
            last_activity=self.last_activity.isoformat() if self.last_activity else None,
            session_age_seconds=(datetime.now() - self.last_activity).total_seconds() if self.last_activity else None
        )
    
    def cleanup(self):
        """Limpa recursos"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
            self.driver = None
        self.logged_in = False

# Instância global do serviço
selenium_service = SeleniumService()

# Funções de autenticação e token com thread safety
import threading
token_lock = threading.Lock()

def validate_token(token: str) -> bool:
    with token_lock:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT token FROM tokens WHERE token = ?', (token,))
        result = cursor.fetchone()
        conn.close()
        return result is not None

def update_token_usage(token: str):
    with token_lock:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE tokens 
            SET usage_count = usage_count + 1, last_used = ?
            WHERE token = ?
        ''', (datetime.now().isoformat(), token))
        conn.commit()
        conn.close()

def create_token() -> str:
    token = secrets.token_urlsafe(32)
    with token_lock:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO tokens (token, created_at, usage_count)
            VALUES (?, ?, 0)
        ''', (token, datetime.now().isoformat()))
        conn.commit()
        conn.close()
    return token

def get_all_tokens():
    with token_lock:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT token, created_at, usage_count, last_used FROM tokens ORDER BY created_at DESC')
        results = cursor.fetchall()
        conn.close()
        return results

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    token = credentials.credentials
    if not validate_token(token):
        raise HTTPException(status_code=401, detail="Token inválido")
    update_token_usage(token)
    return token

def verify_admin_password(admin_password: str = Header(...)):
    if admin_password != ADMIN_PASSWORD:
        raise HTTPException(status_code=401, detail="Senha de admin incorreta")

@app.get("/", 
         response_model=MessageResponse,
         summary="Página inicial",
         description="Retorna informações básicas da API")
async def root():
    """Endpoint raiz da API"""
    return MessageResponse(message="API Consulta Veículos - Sistema Online")

@app.get("/status",
         response_model=StatusResponse,
         summary="Status do sistema",
         description="Retorna o status atual do sistema de consultas",
         tags=["Sistema"])
async def get_status():
    """
    Retorna informações sobre o status do sistema:
    
    - **driver_active**: Se o sistema está ativo
    - **logged_in**: Se está conectado aos serviços
    - **last_activity**: Timestamp da última atividade
    - **session_age_seconds**: Tempo desde a última atividade
    """
    return selenium_service.get_status()

@app.get("/veiculo/{placa}",
         response_model=VehicleResponse,
         responses={
             200: {"description": "Dados do veículo encontrados"},
             400: {"model": ErrorResponse, "description": "Placa inválida"},
             401: {"model": ErrorResponse, "description": "Token inválido"},
             404: {"model": ErrorResponse, "description": "Veículo não encontrado"},
             500: {"model": ErrorResponse, "description": "Erro interno do servidor"}
         },
         summary="Consultar veiculo",
         description="Consulta dados completos de um veículo pela placa. **REQUER TOKEN DE ACESSO**",
         tags=["Consultas"])
async def get_vehicle(
    placa: str = Path(
        ...,
        description="Placa do veículo (formatos aceitos: ABC1234 ou ABC1D23)",
        min_length=7,
        max_length=8
    ),
    token: str = Depends(verify_token)
):
    """
    Consulta dados completos de um veículo pela placa.
    
    **AUTENTICAÇÃO OBRIGATÓRIA**
    
    Retorna informações detalhadas do veículo incluindo:
    - Dados do proprietário
    - Especificações técnicas
    - Informações de registro
    - Características do veículo
    
    **Exemplo:**
    ```bash
    curl -H "Authorization: Bearer seu_token" http://localhost:8000/veiculo/ABC1234
    ```
    """
    try:
        # Validar e normalizar placa
        placa = placa.strip().upper()
        if len(placa) < 7 or len(placa) > 8:
            raise HTTPException(
                status_code=400, 
                detail="Placa deve ter entre 7 e 8 caracteres"
            )
        
        # Buscar dados
        vehicle_data = selenium_service.get_vehicle_data(placa)
        
        if not vehicle_data:
            raise HTTPException(
                status_code=404, 
                detail=f"Veículo com placa {placa} não encontrado"
            )
        
        return VehicleResponse(
            success=True,
            data=vehicle_data,
            timestamp=datetime.now().isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Erro interno do servidor: {str(e)}"
        )

# ADMIN ENDPOINTS
@app.post("/admin/create-token",
          response_model=TokenResponse,
          summary="Gerar novo token",
          description="Gera um novo token de acesso para clientes",
          tags=["Admin"])
async def create_new_token(admin_password: str = Header(..., description="Senha do administrador")):
    """
    Gera um novo token de acesso.
    
    **Cabeçalho obrigatório:**
    - `admin-password`: Senha do administrador
    """
    verify_admin_password(admin_password)
    
    token = create_token()
    return TokenResponse(
        token=token,
        created_at=datetime.now().isoformat(),
        usage_count=0
    )

@app.get("/admin/tokens",
         response_model=List[TokenUsage],
         summary="Listar todos os tokens",
         description="Lista todos os tokens e estatísticas de uso",
         tags=["Admin"])
async def list_tokens(admin_password: str = Header(..., description="Senha do administrador")):
    """
    Lista todos os tokens e estatísticas de uso.
    
    **Cabeçalho obrigatório:**
    - `admin-password`: Senha do administrador
    """
    verify_admin_password(admin_password)
    
    tokens = get_all_tokens()
    return [
        TokenUsage(
            token=token[0],
            created_at=token[1],
            usage_count=token[2],
            last_used=token[3]
        )
        for token in tokens
    ]

@app.post("/admin/reset-session",
          response_model=MessageResponse,
          summary="Reiniciar sistema",
          description="Força reinicialização do sistema",
          tags=["Admin"])
async def reset_session(admin_password: str = Header(..., description="Senha do administrador")):
    """
    Força reinicialização do sistema de consultas.
    
    **Cabeçalho obrigatório:**
    - `admin-password`: Senha do administrador
    """
    verify_admin_password(admin_password)
    
    try:
        selenium_service.logged_in = False
        selenium_service._ensure_session()
        return MessageResponse(message="Sistema reiniciado com sucesso")
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Erro ao reiniciar sistema: {str(e)}"
        )

if __name__ == "__main__":
    print("Iniciando API Consulta Veiculos...")
    uvicorn.run(app, host="0.0.0.0", port=8000)