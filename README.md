# 🚗 API Consulta Veículos

API profissional com autenticação por token para consulta completa de dados veiculares.

## 🚀 Como Usar (Super Simples!)

### 1. Instalar
```bash
pip install -r requirements.txt
```

### 2. Iniciar
```bash
python deploy.py
```

### 3. Acessar
- **API**: http://localhost:8000
- **Swagger**: http://localhost:8000/docs ⭐

## 👨‍💼 Para Admin (Você)

### Gerar Token para Cliente
```bash
curl -H "admin-password: adminbrobot@2025" -X POST http://localhost:8000/admin/create-token
```

### Ver Uso dos Tokens
```bash
curl -H "admin-password: adminbrobot@2025" http://localhost:8000/admin/tokens
```

## 👤 Para Cliente

### Consultar Veículo
```bash
curl -H "Authorization: Bearer TOKEN_AQUI" http://localhost:8000/veiculo/ABC1234
```

## 📊 Características

✅ **Sistema Híbrido**: Combina múltiplas fontes de dados  
✅ **Dados Completos**: 15+ campos de informações veiculares  
✅ **Autenticação**: Tokens seguros com controle de uso  
✅ **Performance**: Sistema de cache e sessão persistente  
✅ **Swagger**: Documentação interativa completa  
✅ **Deploy Fácil**: Pronto para Fly.io com Dockerfile otimizado  

## 🌐 Deploy Online

### Fly.io (Recomendado)
1. Conecte este repositório ao Fly.io
2. Deploy automático será feito usando o Dockerfile
3. Sua API estará online com domínio HTTPS

Veja o `FLYIO_DEPLOY.md` para instruções detalhadas.

### Por que Fly.io?
- ✅ **Grátis para sempre** (não é trial)
- ✅ **Região São Paulo** (baixa latência)
- ✅ **Selenium suportado** nativamente
- ✅ **Performance superior**

## 📱 Estrutura do Projeto

- `api_veiculo.py` - API principal com sistema híbrido
- `deploy.py` - Servidor otimizado para produção
- `requirements.txt` - Dependências Python
- `Dockerfile` - Container otimizado para Fly.io
- `fly.toml` - Configuração Fly.io
- `FLYIO_DEPLOY.md` - Guia completo de deploy
- `tokens.db` - Database SQLite (criado automaticamente)

## 📋 Dados Retornados

A API retorna informações completas do veículo:

```json
{
  "placa": "ABC1234",
  "nome_proprietario": "JOÃO DA SILVA",
  "cpf_cnpj": "12345678901",
  "renavam": "123456789",
  "marca": "VOLKSWAGEN",
  "modelo": "GOL 1.0",
  "cor": "Branco",
  "situacao": "Veículo em circulação",
  "chassi": "9BWZZZ377VT004251",
  "ano_fabricacao": 2020,
  "ano_modelo": 2021,
  "tipo": "Automóvel",
  "especie": "Passageiro",
  "municipio_registro": "SÃO PAULO",
  "uf": "SP",
  "furtado": false,
  "em_deposito": false
}
```

## 🔒 Segurança

- Autenticação obrigatória via token Bearer
- Controle de uso por token
- Logs detalhados de acesso
- Senha de admin configurável
- Dados sensíveis não expostos na documentação