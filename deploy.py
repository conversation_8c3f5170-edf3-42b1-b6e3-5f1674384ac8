#!/usr/bin/env python3
"""
🚀 Deploy Fácil - API Consulta Veículos DER-MG

Servidor de produção simples e robusto.
"""
import uvicorn
from api_veiculo import app

if __name__ == "__main__":
    print("INICIANDO API CONSULTA VEICULOS")
    print("Acesse: http://localhost:8000")
    print("Swagger: http://localhost:8000/docs")
    print("Admin: Gere tokens com senha 'adminbrobot@2025'")
    print("="*50)
    
    # Configuração para produção
    # Usar PORT do ambiente se disponível (Fly.io)
    import os
    port = int(os.environ.get("PORT", 8000))
    
    print(f"Iniciando servidor na porta {port}")
    
    uvicorn.run(
        app,
        host="0.0.0.0",  # Aceita conexões de qualquer IP
        port=port,
        workers=1,       # 1 worker para manter sessão única
        reload=False,    # Sem reload em produção
        access_log=True, # Log de acessos
        log_level="info"
    )